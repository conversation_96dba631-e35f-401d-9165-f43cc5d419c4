<?xml version='1.0' encoding='UTF-8'?>
<Program Name="TestProgram" TestEdits="false" MainRoutineName="Main" Disabled="false" UseAsFolder="false">
  <Description><![CDATA[Test Program for AOI Unit Testing]]></Description>
  <Tags>
    <!-- Test tags for aoi_Interlocks unit tests -->
    <Tag Name="TestInterlock" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test instance of aoi_Interlocks]]></Description>
    </Tag>
    <Tag Name="TestInterlock1" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 1 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock2" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 2 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock3" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 3 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock4" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 4 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock5" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 5 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock6" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 6 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock7" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 7 input]]></Description>
    </Tag>
    <Tag Name="TestInterlock8" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Interlock 8 input]]></Description>
    </Tag>
    <Tag Name="TestReset" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test Reset input]]></Description>
    </Tag>
    <Tag Name="TestInterlockOK" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test InterlockOK output]]></Description>
    </Tag>
    <Tag Name="TestFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test FirstOutNum output]]></Description>
    </Tag>
    <Tag Name="TestFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test FirstOutDesc output]]></Description>
    </Tag>
    <Tag Name="TestInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test InterlockStatus output]]></Description>
    </Tag>
    <Tag Name="TestCase" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test case number to run]]></Description>
    </Tag>
    <Tag Name="TestStart" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Start test execution]]></Description>
    </Tag>
    <Tag Name="TestComplete" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test execution complete]]></Description>
    </Tag>
    <Tag Name="TestPassed" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test passed indicator]]></Description>
    </Tag>
    <Tag Name="TestDescription" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test result description]]></Description>
    </Tag>
    <Tag Name="TestRun1" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 1 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun2" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 2 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun3" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 3 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun4" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 4 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun5" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 5 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun6" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 6 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun7" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 7 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun8" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 8 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun9" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 9 running indicator]]></Description>
    </Tag>
    <Tag Name="TestRun10" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test 10 running indicator]]></Description>
    </Tag>
    <Tag Name="TestTimer" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test timer 1]]></Description>
    </Tag>
    <Tag Name="TestTimer2" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test timer 2]]></Description>
    </Tag>
    <Tag Name="TestTimer3" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test timer 3]]></Description>
    </Tag>
    <Tag Name="TestTimer4" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test timer 4]]></Description>
    </Tag>
    <Tag Name="TestPassedInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test passed indicator light]]></Description>
    </Tag>
    <Tag Name="TestFailedInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Test failed indicator light]]></Description>
    </Tag>

    <!-- Coal Handling System Test Tags -->
    <Tag Name="Test_CoalSystem_1_Pass" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 1 pass indicator]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_1_Description" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 1 description]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_2_Pass" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 2 pass indicator]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_2_Description" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 2 description]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_3_Pass" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 3 pass indicator]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_3_Description" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 3 description]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_4_Pass" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 4 pass indicator]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_4_Description" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 4 description]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_5_Pass" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 5 pass indicator]]></Description>
    </Tag>
    <Tag Name="Test_CoalSystem_5_Description" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal System Test 5 description]]></Description>
    </Tag>

    <!-- Coal System Test Control Tags -->
    <Tag Name="CoalSystemTestCase" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal system test case number]]></Description>
    </Tag>
    <Tag Name="CoalSystemTestStart" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Start coal system test]]></Description>
    </Tag>
    <Tag Name="CoalSystemTestComplete" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal system test complete]]></Description>
    </Tag>
    <Tag Name="CoalSystemTestTimer" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Coal system test timer]]></Description>
    </Tag>

  </Tags>
</Program>
