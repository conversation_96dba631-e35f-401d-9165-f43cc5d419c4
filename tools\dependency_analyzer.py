#!/usr/bin/env python3
"""
L5X Tag Dependency Analyzer

This tool analyzes L5X files to identify dependencies when changing tag configurations.
It helps identify potential issues before making changes to tag definitions.
"""

import xml.etree.ElementTree as ET
import re
import argparse
from pathlib import Path
from typing import Dict, List, Set, Tuple

class L5XDependencyAnalyzer:
    def __init__(self, l5x_file_path: str):
        self.l5x_file = l5x_file_path
        self.tree = ET.parse(l5x_file_path)
        self.root = self.tree.getroot()
        self.tags = {}
        self.dependencies = {}
        
    def extract_tags(self) -> Dict[str, Dict]:
        """Extract all tags from the L5X file"""
        tags = {}
        
        # Controller tags
        controller_tags = self.root.findall(".//Controller/Tags/Tag")
        for tag in controller_tags:
            tag_name = tag.get('Name')
            tags[tag_name] = {
                'scope': 'Controller',
                'data_type': tag.get('DataType'),
                'usage': tag.get('Usage', 'InOut'),
                'external_access': tag.get('ExternalAccess', 'Read/Write'),
                'dimensions': tag.get('Dimensions'),
                'element': tag
            }
        
        # Program tags
        programs = self.root.findall(".//Programs/Program")
        for program in programs:
            program_name = program.get('Name')
            program_tags = program.findall(".//Tags/Tag")
            for tag in program_tags:
                tag_name = f"{program_name}.{tag.get('Name')}"
                tags[tag_name] = {
                    'scope': f'Program:{program_name}',
                    'data_type': tag.get('DataType'),
                    'usage': tag.get('Usage', 'InOut'),
                    'external_access': tag.get('ExternalAccess', 'Read/Write'),
                    'dimensions': tag.get('Dimensions'),
                    'element': tag
                }
        
        self.tags = tags
        return tags
    
    def find_tag_references(self, tag_name: str) -> List[Dict]:
        """Find all references to a specific tag"""
        references = []
        
        # Search in routine logic
        routines = self.root.findall(".//Routines/Routine")
        for routine in routines:
            routine_name = routine.get('Name')
            program = routine.find("../../..")
            program_name = program.get('Name') if program is not None else 'Unknown'
            
            # Search in RLL content
            rll_content = routine.find(".//RLLContent")
            if rll_content is not None:
                for rung in rll_content.findall("Rung"):
                    rung_number = rung.get('Number')
                    text_element = rung.find("Text")
                    if text_element is not None and text_element.text:
                        if tag_name in text_element.text:
                            references.append({
                                'type': 'Routine',
                                'location': f"{program_name}.{routine_name}",
                                'rung': rung_number,
                                'context': text_element.text[:100] + "..." if len(text_element.text) > 100 else text_element.text
                            })
        
        # Search in AOI definitions
        aoi_definitions = self.root.findall(".//AddOnInstructionDefinitions/AddOnInstructionDefinition")
        for aoi in aoi_definitions:
            aoi_name = aoi.get('Name')
            # Check parameters
            parameters = aoi.findall(".//Parameters/Parameter")
            for param in parameters:
                if param.get('DataType') == tag_name:
                    references.append({
                        'type': 'AOI Parameter',
                        'location': f"AOI:{aoi_name}",
                        'parameter': param.get('Name'),
                        'context': f"Parameter data type reference"
                    })
            
            # Check AOI logic
            aoi_routines = aoi.findall(".//Routines/Routine")
            for routine in aoi_routines:
                routine_name = routine.get('Name')
                rll_content = routine.find(".//RLLContent")
                if rll_content is not None:
                    for rung in rll_content.findall("Rung"):
                        rung_number = rung.get('Number')
                        text_element = rung.find("Text")
                        if text_element is not None and text_element.text:
                            if tag_name in text_element.text:
                                references.append({
                                    'type': 'AOI Routine',
                                    'location': f"AOI:{aoi_name}.{routine_name}",
                                    'rung': rung_number,
                                    'context': text_element.text[:100] + "..." if len(text_element.text) > 100 else text_element.text
                                })
        
        return references
    
    def analyze_data_type_dependencies(self, tag_name: str) -> Dict:
        """Analyze dependencies related to data type changes"""
        if tag_name not in self.tags:
            return {'error': f"Tag '{tag_name}' not found"}
        
        tag_info = self.tags[tag_name]
        current_data_type = tag_info['data_type']
        references = self.find_tag_references(tag_name)
        
        analysis = {
            'tag_name': tag_name,
            'current_data_type': current_data_type,
            'references': references,
            'potential_issues': []
        }
        
        # Analyze potential issues based on current usage
        for ref in references:
            if ref['type'] in ['Routine', 'AOI Routine']:
                context = ref.get('context', '')
                
                # Check for bit-level operations
                if re.search(r'\b(XIC|XIO|OTE|OTL|OTU)\b', context):
                    if current_data_type != 'BOOL':
                        analysis['potential_issues'].append({
                            'type': 'Bit Operation on Non-BOOL',
                            'location': ref['location'],
                            'description': 'Bit-level operations require BOOL data type'
                        })
                
                # Check for array indexing
                if re.search(r'\[.*\]', context):
                    analysis['potential_issues'].append({
                        'type': 'Array Indexing',
                        'location': ref['location'],
                        'description': 'Array dimension changes may affect indexing logic'
                    })
                
                # Check for mathematical operations
                if re.search(r'\b(ADD|SUB|MUL|DIV|MOD)\b', context):
                    analysis['potential_issues'].append({
                        'type': 'Mathematical Operation',
                        'location': ref['location'],
                        'description': 'Data type changes may affect mathematical operations'
                    })
        
        return analysis
    
    def generate_dependency_report(self, tag_names: List[str] = None) -> Dict:
        """Generate a comprehensive dependency report"""
        if tag_names is None:
            tag_names = list(self.tags.keys())
        
        report = {
            'summary': {
                'total_tags': len(self.tags),
                'analyzed_tags': len(tag_names),
                'total_issues': 0
            },
            'tag_analysis': {}
        }
        
        for tag_name in tag_names:
            analysis = self.analyze_data_type_dependencies(tag_name)
            report['tag_analysis'][tag_name] = analysis
            report['summary']['total_issues'] += len(analysis.get('potential_issues', []))
        
        return report
    
    def export_cross_reference(self, output_file: str):
        """Export cross-reference information to a file"""
        with open(output_file, 'w') as f:
            f.write("L5X Tag Cross-Reference Report\n")
            f.write("=" * 50 + "\n\n")
            
            for tag_name, tag_info in self.tags.items():
                f.write(f"Tag: {tag_name}\n")
                f.write(f"  Data Type: {tag_info['data_type']}\n")
                f.write(f"  Scope: {tag_info['scope']}\n")
                f.write(f"  External Access: {tag_info['external_access']}\n")
                
                references = self.find_tag_references(tag_name)
                if references:
                    f.write(f"  References ({len(references)}):\n")
                    for ref in references:
                        f.write(f"    - {ref['type']}: {ref['location']}\n")
                        if 'rung' in ref:
                            f.write(f"      Rung: {ref['rung']}\n")
                        f.write(f"      Context: {ref['context'][:80]}...\n")
                else:
                    f.write("  No references found\n")
                
                f.write("\n" + "-" * 40 + "\n\n")

def main():
    parser = argparse.ArgumentParser(description='Analyze L5X tag dependencies')
    parser.add_argument('l5x_file', help='Path to L5X file')
    parser.add_argument('--tag', help='Specific tag to analyze')
    parser.add_argument('--output', help='Output file for cross-reference report')
    parser.add_argument('--report', action='store_true', help='Generate full dependency report')
    
    args = parser.parse_args()
    
    if not Path(args.l5x_file).exists():
        print(f"Error: File '{args.l5x_file}' not found")
        return
    
    analyzer = L5XDependencyAnalyzer(args.l5x_file)
    analyzer.extract_tags()
    
    if args.tag:
        # Analyze specific tag
        analysis = analyzer.analyze_data_type_dependencies(args.tag)
        print(f"Analysis for tag '{args.tag}':")
        print(f"Data Type: {analysis.get('current_data_type', 'Unknown')}")
        print(f"References: {len(analysis.get('references', []))}")
        print(f"Potential Issues: {len(analysis.get('potential_issues', []))}")
        
        for issue in analysis.get('potential_issues', []):
            print(f"  - {issue['type']} at {issue['location']}: {issue['description']}")
    
    elif args.report:
        # Generate full report
        report = analyzer.generate_dependency_report()
        print(f"Dependency Report Summary:")
        print(f"Total Tags: {report['summary']['total_tags']}")
        print(f"Total Issues: {report['summary']['total_issues']}")
        
        for tag_name, analysis in report['tag_analysis'].items():
            if analysis.get('potential_issues'):
                print(f"\nTag '{tag_name}' has {len(analysis['potential_issues'])} potential issues:")
                for issue in analysis['potential_issues']:
                    print(f"  - {issue['type']}: {issue['description']}")
    
    if args.output:
        analyzer.export_cross_reference(args.output)
        print(f"Cross-reference report exported to '{args.output}'")

if __name__ == "__main__":
    main()
