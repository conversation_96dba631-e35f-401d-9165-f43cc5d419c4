# Coal Material Handling System

## Overview

The Coal Material Handling System is designed to transport coal from the feed point through a series of conveyors to a storage tank and then to the discharge point. The system consists of three conveyors, a storage tank, and associated monitoring equipment.

## Equipment List

### Conveyors and Motors

| Equipment | Description | Motor | Type |
|-----------|-------------|-------|------|
| CV101 | Feed Conveyor | MO101 | Belt Conveyor, 24" wide, 100 ft long |
| CV102 | Transfer Conveyor | MO102 | Belt Conveyor, 24" wide, 150 ft long |
| CV103 | Discharge Conveyor | MO103 | Belt Conveyor, 24" wide, 75 ft long |

### Storage Tank

| Equipment | Description | Capacity | Material |
|-----------|-------------|----------|----------|
| TK101 | Coal Storage Tank | 500 tons | Carbon Steel with liner |

### Instrumentation

| Tag | Description | Type | Location |
|-----|-------------|------|----------|
| DS101 | Feed Conveyor Drift Switch | Belt Speed Monitor | CV101 Head Pulley |
| DS102 | Transfer Conveyor Drift Switch | Belt Speed Monitor | CV102 Head Pulley |
| DS103 | Discharge Conveyor Drift Switch | Belt Speed Monitor | CV103 Head Pulley |
| LS101 | Tank High Level | Ultrasonic Level | TK101 Top |
| LS102 | Tank Low Level | Ultrasonic Level | TK101 Side |
| LS103 | Tank Low-Low Level | Ultrasonic Level | TK101 Bottom |

## Control Strategy

### Operating Modes

#### Manual Mode
- Individual conveyor control by operator
- Manual start/stop commands for each motor
- Override of automatic interlocks (with safety limits)

#### Automatic Mode
- Sequential startup and shutdown
- Automatic interlock protection
- Level-based control

### Startup Sequence (Automatic Mode)

1. **Pre-Start Checks**
   - All drift switches operational
   - Tank not at high level
   - Tank above low-low level
   - No system faults

2. **Sequential Start**
   - Start MO103 (Discharge Conveyor) first
   - Wait 2 seconds, then start MO102 (Transfer Conveyor)
   - Wait 2 seconds, then start MO101 (Feed Conveyor)

3. **Running Conditions**
   - All conveyors running
   - Continuous monitoring of drift switches
   - Tank level monitoring

### Shutdown Sequence (Automatic Mode)

1. **Normal Shutdown**
   - Stop MO101 (Feed Conveyor) first
   - Wait 2 seconds, then stop MO102 (Transfer Conveyor)
   - Wait 2 seconds, then stop MO103 (Discharge Conveyor)

2. **Emergency Shutdown**
   - Immediate stop of all conveyors
   - Triggered by:
     - Any drift switch failure
     - Tank high level
     - Tank low-low level
     - System fault
     - Emergency stop command

## Interlocks

### Start Interlocks

#### MO103 (Discharge Conveyor)
- Tank level above low-low (LS103)
- Tank level above low (LS102) - material available
- Drift switch DS103 operational
- No system fault

#### MO102 (Transfer Conveyor)
- MO103 running
- Drift switch DS102 operational
- No system fault

#### MO101 (Feed Conveyor)
- MO102 running
- Tank level below high (LS101) - space available
- Drift switch DS101 operational
- No system fault

### Run Interlocks

All motors have the same run interlocks as their start interlocks. Loss of any run interlock will stop the respective motor.

## Safety Features

### Drift Switch Monitoring
- Continuous monitoring of belt speed
- Immediate shutdown on belt slip detection
- Prevents material spillage and equipment damage

### Level Protection
- High level stops feed conveyor to prevent overflow
- Low-low level stops discharge conveyor to prevent running empty
- Level sensors provide redundant protection

### Sequential Control
- Proper startup sequence prevents material backup
- Proper shutdown sequence ensures complete material transfer
- Time delays prevent mechanical stress

## Alarms and Diagnostics

### System Alarms

| Alarm | Description | Action |
|-------|-------------|--------|
| COAL_SYSTEM_FAULT | General system fault | Stop all conveyors |
| DS101_FAULT | Feed conveyor belt slip | Stop MO101 |
| DS102_FAULT | Transfer conveyor belt slip | Stop MO102 |
| DS103_FAULT | Discharge conveyor belt slip | Stop MO103 |
| LS101_HIGH | Tank high level | Stop MO101 |
| LS103_LOW_LOW | Tank low-low level | Stop MO103 |

### Diagnostic Information

- Motor run hours
- Start counts
- Fault history
- Performance metrics

## Maintenance

### Preventive Maintenance

#### Daily Checks
- Visual inspection of belts
- Check for material spillage
- Verify level sensor readings

#### Weekly Checks
- Drift switch calibration
- Belt tension inspection
- Motor current readings

#### Monthly Checks
- Belt alignment
- Pulley inspection
- Bearing lubrication

### Troubleshooting

#### Common Issues

1. **Belt Slip (Drift Switch Alarm)**
   - Check belt tension
   - Inspect drive pulley
   - Check for material buildup

2. **Level Sensor Faults**
   - Clean sensor face
   - Check wiring connections
   - Verify calibration

3. **Motor Faults**
   - Check motor overloads
   - Inspect contactors
   - Verify control power

## SCADA Interface

### Control Points
- System start/stop
- Mode selection (Auto/Manual)
- Individual motor control (Manual mode)
- Fault reset

### Status Points
- System running status
- Individual motor status
- Tank levels
- Drift switch status
- Alarm status

### Trending
- Motor currents
- Tank level
- Run hours
- System availability

## Configuration Parameters

### Timing Parameters
- Start delay between motors: 2 seconds
- Stop delay between motors: 2 seconds
- Siren pre-start time: 3 seconds
- Motor starting timeout: 8 seconds
- Motor stopping timeout: 3 seconds

### Level Setpoints
- High level alarm: 95% of tank capacity
- Low level alarm: 20% of tank capacity
- Low-low level alarm: 5% of tank capacity

## Implementation Notes

### PLC Programming
- Uses standardized aoi_Motor AOI for motor control
- Implements sequential control with timer-based delays
- Provides comprehensive interlock protection
- Includes SCADA communication interface

### I/O Requirements
- Digital inputs: Drift switches, level switches, motor feedbacks
- Digital outputs: Motor starters, sirens, status lights
- Analog inputs: Level transmitters (if used)
- Communication: SCADA interface tags

### Testing
- Individual motor testing in manual mode
- Sequential startup/shutdown testing
- Interlock testing with simulated faults
- Emergency stop testing
