<?xml version='1.0' encoding='UTF-8'?>
<DataType Name="UDT_SCADA" Family="NoFamily" Class="User">
  <Description><![CDATA[SCADA Communication UDT for motor and valve control interfaces]]></Description>
  <Members>
    <Member Name="StartCommand" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[SCADA start command]]></Description>
    </Member>
    <Member Name="StopCommand" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[SCADA stop command]]></Description>
    </Member>
    <Member Name="AutoCommand" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[SCADA auto mode command]]></Description>
    </Member>
    <Member Name="ManualCommand" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[SCADA manual mode command]]></Description>
    </Member>
    <Member Name="ResetCommand" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[SCADA reset command]]></Description>
    </Member>
    <Member Name="RunningStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment running status to SCADA]]></Description>
    </Member>
    <Member Name="StartingStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment starting status to SCADA]]></Description>
    </Member>
    <Member Name="StoppingStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment stopping status to SCADA]]></Description>
    </Member>
    <Member Name="AutoStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment auto mode status to SCADA]]></Description>
    </Member>
    <Member Name="ManualStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment manual mode status to SCADA]]></Description>
    </Member>
    <Member Name="FaultStatus" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment fault status to SCADA]]></Description>
    </Member>
    <Member Name="SpeedSetpoint" DataType="REAL" Dimension="0" Radix="Float" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Speed setpoint from SCADA (for VFD motors)]]></Description>
    </Member>
    <Member Name="SpeedFeedback" DataType="REAL" Dimension="0" Radix="Float" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Speed feedback to SCADA (for VFD motors)]]></Description>
    </Member>
    <Member Name="CurrentFeedback" DataType="REAL" Dimension="0" Radix="Float" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Motor current feedback to SCADA]]></Description>
    </Member>
    <Member Name="PowerFeedback" DataType="REAL" Dimension="0" Radix="Float" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Motor power feedback to SCADA]]></Description>
    </Member>
    <Member Name="RunHours" DataType="REAL" Dimension="0" Radix="Float" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment run hours to SCADA]]></Description>
    </Member>
    <Member Name="StartCount" DataType="DINT" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Equipment start count to SCADA]]></Description>
    </Member>
    <Member Name="AlarmCode" DataType="DINT" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Current alarm code to SCADA]]></Description>
    </Member>
    <Member Name="AlarmDescription" DataType="STRING" Dimension="0" Radix="NullType" Hidden="false" ExternalAccess="Read Only">
      <Description><![CDATA[Current alarm description to SCADA]]></Description>
    </Member>
    <Member Name="Heartbeat" DataType="BOOL" Dimension="0" Radix="Decimal" Hidden="false" ExternalAccess="Read/Write">
      <Description><![CDATA[Communication heartbeat with SCADA]]></Description>
    </Member>
  </Members>
</DataType>
