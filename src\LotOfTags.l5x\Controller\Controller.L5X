<?xml version='1.0' encoding='UTF-8'?>
<RSLogix5000Content SchemaRevision="1.0" SoftwareRevision="32.02" TargetName="TestController" TargetType="Controller" ContainsContext="false" Owner="tnunnink, EN Engineering" ExportDate="Sun Aug 06 10:46:35 2023" ExportOptions="NoRawData L5KData DecoratedData ForceProtectedEncoding AllProjDocTrans">
  <Controller Use="Target" Name="TestController" ProcessorType="1756-L83E" MajorRev="32" MinorRev="11" ProjectCreationDate="Mon Sep 27 16:23:27 2021" LastModifiedDate="Sun Aug 06 10:46:15 2023" SFCExecutionControl="CurrentActive" SFCRestartPosition="MostRecent" SFCLastScan="DontScan" ProjectSN="16#0000_0000" MatchProjectToController="false" CanUseRPIFromProducer="false" InhibitAutomaticFirmwareUpdate="0" PassThroughConfiguration="EnabledWithAppend" DownloadProjectDocumentationAndExtendedProperties="true" DownloadProjectCustomProperties="true" ReportMinorOverflow="false">
    <Description><![CDATA[This is a test project]]></Description>
    <RedundancyInfo Enabled="false" KeepTestEditsOnSwitchOver="false"/>
    <Security Code="0" ChangesToDetect="16#ffff_ffff_ffff_ffff"/>
    <SafetyInfo/>
    <ParameterConnections>
      <ParameterConnection EndPoint1="\NProgram.InOutTag" EndPoint2="FlexIO:1:I.Ch00.RollingTimestamp"/>
    </ParameterConnections>
    <CST MasterID="0"/>
    <WallClockTime LocalTimeAdjustment="0" TimeZone="0"/>
    <Trends>
      <Trend Name="Test" SamplePeriod="10" NumberOfCaptures="1" CaptureSizeType="Samples" CaptureSize="60001" StartTriggerType="Event Trigger" StartTriggerTag1="_Test" StartTriggerOperation1="0" StartTriggerTargetType1="Target Tag" StartTriggerTargetTag1="AsciiTag" StartTriggerLogicalOperation="AND" StartTriggerTag2="AsciiTag" StartTriggerOperation2="1" StartTriggerTargetType2="Target Value" StartTriggerTargetValue2="1234" PreSampleType="Time Period" PreSamples="2000" StopTriggerType="No Trigger" TrendxVersion="5.2">
        <Description><![CDATA[This is a test]]></Description>
        <Template>208 207 17 224 161 177 26 225 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 62 0 3 0 254 255 9 0 6 0 0 0 0 0
                    0 0
                    0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 16 0 0 2 0 0 0 1 0 0 0 254 255 255 255 0 0 0 0 0 0 0 0
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 253 255 255 255 254 255 255 255
                    254 255 255 255 4 0 0 0 5 0 0 0 254 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 82 0
                    111 0 111 0 116 0 32 0 69 0 110 0 116 0
                    114 0 121 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 22 0 5 0 255 255 255 255 255 255 255 255 1 0 0 0 14 52 81 191 9 48 205 65 159 190
                    222 136 174 167 50 40
                    0 0 0 0 0 0 0 0 0 0 0 0 224 29 228 49 217 165 217 1 3 0 0 0 64 4 0 0 0 0 0 0 67 0 111 0 110 0 116 0
                    101 0 110 0 116 0 115 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 18 0 2 1 255 255 255 255 255 255 255 255 255 255 255 255 0 0 0 0 0 0
                    0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 4 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 255 255 255 255 255 255 255 255 255 255
                    255
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 255 255 255
                    255 255 255 255 255 255 255 255 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 2 0 0 0 3 0 0 0 4 0 0 0 5 0 0 0 6 0 0 0
                    7 0 0 0 8 0 0 0 9 0 0 0 10 0 0 0 11 0 0 0 12 0 0 0 13 0 0 0 14 0 0 0 15 0 0 0 16 0 0 0
                    254 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 255 255 255 255 255 255 255
                    255 255 255 255 255 255 255 255 4 0 0 0 131 157 0 0 202 79 0 0 0 0 0 0 13 0 0 0 255 254 255 4 84 0
                    101 0 115 0 116 0
                    0 0 0 0 0 0 0 0 255 255 255 255 200 0 0 0 255 157 3 0 255 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 254
                    255
                    0 255 254 255 0 1 96 234 0 0 0 0 0 0 255 255 255 0 0 0 0 0 0 0 0 0 0 166 3 0 0 2 0 255 255 1 0 13 0
                    67
                    86 105 101 119 76 105 110 101 73 110 102 111 3 0 0 0 1 0 0 0 0 0 0 0 0 0 200 66 255 254 255 0 255
                    254 255 0 0 0 0 0
                    0 0 0 0 3 0 0 0 0 0 0 0 255 254 255 0 255 254 255 0 255 254 255 0 255 254 255 5 95 0 84 0 101 0 115
                    0 116 0 32 1
                    0 0 0 255 254 255 0 255 254 255 0 1 128 3 0 0 0 1 0 0 0 0 0 0 0 0 0 200 66 255 254 255 0 255 254 255
                    0 0 0 0
                    0 0 0 0 0 3 0 0 0 0 0 0 0 255 254 255 0 255 254 255 0 255 254 255 0 255 254 255 8 65 0 115 0 99 0
                    105 0 105 0 84
                    0 97 0 103 0 32 1 0 0 0 255 254 255 0 255 254 255 0 9 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 1 0
                    0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0 0 0 1 0
                    0 0 2 0 0 0 255 0 0 255 0 0 0 0 0 0 0 0 0 0 4 0 0 0 4 0 0 0 0 0 0 0 0 0 0 0 0 0 200 66
                    0 0 0 0 0 0 0 0 12 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 2 0 1 0 0 0 1 0 0 0 2 0 0 0 0 0
                    0 0 0 0 2 0 1 0 0 0 1 0 0 0 2 0 0 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 44 0 0 0 0 0 0 0
                    0 0 0 0 12 0 0 0 0 0 0 0 0 0 136 4 28 51 157 91 0 0 0 0 0 0 0 0 12 0 0 0 0 0 0 0 128 128 128 0
                    128 128 128 0 255 239 255 254 255 191 255 247 175 154 149 100 1 0 0 0 152 3 0 0 177 154 149 100 1 0
                    0 0 152 3 0 0 255 254 255 0
                    1 0 0 0 1 0 0 0 2 0 0 0 0 0 0 0 0 0 2 0 0 0 40 0 0 0 50 0 0 0 0 0 0 0 0 0 0 0 92 0
                    0 0 245 255 255 255 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 254
                    255 5 65 0 114 0 105 0 97 0 108 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 3 0 0 0 0 0 0 0 255 254 255 0 255 254 255 0 255 254 255 0 255 254 255 0 32 1 0 0 0 255 254
                    255 0 255 254 255
                    0 3 0 0 0 0 0 0 0 255 254 255 0 255 254 255 0 255 254 255 0 255 254 255 0 32 1 0 0 0 255 254 255 0
                    255 254 255 0 0 0
                    0 0 0 0 0 0 255 255 255 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 8 0 0 0 2 0 0 0 0 0 0 0 0 0
                    2 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 255 255 1 0 9 0 67 76 105 110 101 73 110 102 111 3 0 0 0 1 0 0 0 0
                    0 0 0 0 0 200 66 255 254 255 0 255 254 255 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 255 254 255 0 255 254
                    255 0 255
                    254 255 0 255 254 255 5 95 0 84 0 101 0 115 0 116 0 32 1 0 0 0 255 254 255 0 255 254 255 0 4 128 3 0
                    0 0 1 0 0 0
                    0 0 0 0 0 0 200 66 255 254 255 0 255 254 255 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 255 254 255 0 255 254
                    255 0
                    255 254 255 0 255 254 255 8 65 0 115 0 99 0 105 0 105 0 84 0 97 0 103 0 32 1 0 0 0 255 254 255 0 255
                    254 255 0 0 0 0
                    0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
                    0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 247 51 3 0
                </Template>
        <Pens>
          <Pen Name="_Test" Color="16#00ff_0000" Visible="true" Style="0" Type="Analog" Width="1" Marker="0" Min="0.0" Max="100.0"/>
          <Pen Name="AsciiTag" Color="16#0000_ff00" Visible="true" Style="0" Type="Analog" Width="1" Marker="0" Min="0.0" Max="100.0"/>
        </Pens>
      </Trend>
    </Trends>
    <DataLogs/>
    <TimeSynchronize Priority1="128" Priority2="128" PTPEnable="false"/>
    <EthernetPorts>
      <EthernetPort Port="1" Label="1" PortEnabled="true"/>
    </EthernetPorts>
    <Tags>
      <Tag Name="VLV01_IsOpen" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Controller tag indicating VLV01 is open - used for interlocking]]></Description>
        <Comments><![CDATA[This tag is used to interlock the PU101 motor with the VLV01 valve]]></Comments>
      </Tag>

      <!-- Coal Material Handling System Tags -->

      <!-- Conveyor Motors -->
      <Tag Name="MO101_Running" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Feed Conveyor Motor MO101 running status]]></Description>
        <Comments><![CDATA[Used for conveyor sequence interlocking]]></Comments>
      </Tag>
      <Tag Name="MO102_Running" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Transfer Conveyor Motor MO102 running status]]></Description>
        <Comments><![CDATA[Used for conveyor sequence interlocking]]></Comments>
      </Tag>
      <Tag Name="MO103_Running" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Discharge Conveyor Motor MO103 running status]]></Description>
        <Comments><![CDATA[Used for conveyor sequence interlocking]]></Comments>
      </Tag>

      <!-- Drift Switches -->
      <Tag Name="DS101_OK" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Feed Conveyor CV101 drift switch - belt moving OK]]></Description>
        <Comments><![CDATA[Monitors belt slip on feed conveyor]]></Comments>
      </Tag>
      <Tag Name="DS102_OK" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Transfer Conveyor CV102 drift switch - belt moving OK]]></Description>
        <Comments><![CDATA[Monitors belt slip on transfer conveyor]]></Comments>
      </Tag>
      <Tag Name="DS103_OK" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Discharge Conveyor CV103 drift switch - belt moving OK]]></Description>
        <Comments><![CDATA[Monitors belt slip on discharge conveyor]]></Comments>
      </Tag>

      <!-- Tank Level Sensors -->
      <Tag Name="LS101_High" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Coal Tank TK101 high level sensor]]></Description>
        <Comments><![CDATA[Stops feed conveyor when tank is full]]></Comments>
      </Tag>
      <Tag Name="LS102_Low" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Coal Tank TK101 low level sensor]]></Description>
        <Comments><![CDATA[Starts discharge conveyor when material available]]></Comments>
      </Tag>
      <Tag Name="LS103_LowLow" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Coal Tank TK101 low-low level sensor]]></Description>
        <Comments><![CDATA[Emergency stop for discharge conveyor]]></Comments>
      </Tag>

      <!-- System Status -->
      <Tag Name="COAL_SYSTEM_RUNNING" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Coal material handling system running status]]></Description>
        <Comments><![CDATA[Overall system status for SCADA and interlocking]]></Comments>
      </Tag>
      <Tag Name="COAL_SYSTEM_FAULT" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
        <Description><![CDATA[Coal material handling system fault status]]></Description>
        <Comments><![CDATA[Any fault in the coal handling system]]></Comments>
      </Tag>

    </Tags>
  </Controller>
</RSLogix5000Content>
