<?xml version='1.0' encoding='UTF-8'?>
<Routine Name="CoalHandlingSystem" Type="RLL">
  <Description><![CDATA[
Coal Material Handling System Control Routine

This routine controls the coal material handling system consisting of:
- CV101: Feed Conveyor (Motor MO101)
- CV102: Transfer Conveyor (Motor MO102) 
- CV103: Discharge Conveyor (Motor MO103)
- TK101: Coal Storage Tank
- DS101-DS103: Drift switches for belt monitoring
- LS101-LS103: Tank level sensors

Control Strategy:
- Sequential startup: MO103 -> MO102 -> MO101
- Sequential shutdown: MO101 -> MO102 -> MO103
- Drift switch monitoring for belt slip detection
- Tank level interlocks for feed and discharge control
- Emergency stop on any fault condition

Operating Sequence:
1. Start discharge conveyor MO103 first
2. Start transfer conveyor MO102 when MO103 is running
3. Start feed conveyor MO101 when MO102 is running and tank not high
4. Stop in reverse order when commanded or on fault
]]></Description>
  <RLLContent>
    <Rung Number="0" Type="N">
      <Comment><![CDATA[Coal System Mode Control
Handle auto/manual mode selection for the entire coal handling system]]></Comment>
      <Text><![CDATA[XIC(COAL_SYSTEM_MANUAL)OTU(COAL_SYSTEM_AUTO);]]></Text>
    </Rung>
    <Rung Number="1" Type="N">
      <Text><![CDATA[XIC(COAL_SYSTEM_AUTO)[OTE(MO101_Auto) ,OTE(MO102_Auto) ,OTE(MO103_Auto) ];]]></Text>
    </Rung>
    <Rung Number="2" Type="N">
      <Text><![CDATA[XIC(COAL_SYSTEM_MANUAL)[OTE(MO101_Manual) ,OTE(MO102_Manual) ,OTE(MO103_Manual) ];]]></Text>
    </Rung>
    <Rung Number="3" Type="N">
      <Comment><![CDATA[Coal System Fault Detection
Monitor for any system faults including drift switches and level sensors]]></Comment>
      <Text><![CDATA[[XIO(DS101_OK) ,XIO(DS102_OK) ,XIO(DS103_OK) ,XIC(LS101_High) ,XIO(LS103_LowLow) ]OTE(COAL_SYSTEM_FAULT);]]></Text>
    </Rung>
    <Rung Number="4" Type="N">
      <Comment><![CDATA[Coal System Running Status
System is running when all three conveyors are running]]></Comment>
      <Text><![CDATA[XIC(MO101_RunningInd)XIC(MO102_RunningInd)XIC(MO103_RunningInd)OTE(COAL_SYSTEM_RUNNING);]]></Text>
    </Rung>
    <Rung Number="5" Type="N">
      <Comment><![CDATA[Coal System Emergency Stop
Stop all conveyors immediately on system fault or stop command]]></Comment>
      <Text><![CDATA[[XIC(COAL_SYSTEM_FAULT) ,XIC(COAL_SYSTEM_STOP) ][OTE(MO101_Stop) ,OTE(MO102_Stop) ,OTE(MO103_Stop) ];]]></Text>
    </Rung>
    <Rung Number="6" Type="N">
      <Comment><![CDATA[Discharge Conveyor MO103 Control
Start first in sequence, stop last]]></Comment>
      <Text><![CDATA[aoi_Motor(MO103,
             MO103_Start,
             MO103_Stop,
             MO103_Auto,
             MO103_Manual,
             3000,
             8000,
             3000,
             15000,
             10000,
             MO103_RunFeedback,
             MO103_StartInterlocks,
             MO103_RunInterlocks,
             MO103_Run,
             MO103_Siren,
             MO103_RunningInd,
             MO103_StartingInd,
             MO103_StoppingInd,
             MO103_AutoInd,
             MO103_ManualInd,
             MO103_SCADA,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0);]]></Text>
    </Rung>
    <Rung Number="7" Type="N">
      <Comment><![CDATA[Transfer Conveyor MO102 Control
Start second in sequence]]></Comment>
      <Text><![CDATA[aoi_Motor(MO102,
             MO102_Start,
             MO102_Stop,
             MO102_Auto,
             MO102_Manual,
             3000,
             8000,
             3000,
             15000,
             10000,
             MO102_RunFeedback,
             MO102_StartInterlocks,
             MO102_RunInterlocks,
             MO102_Run,
             MO102_Siren,
             MO102_RunningInd,
             MO102_StartingInd,
             MO102_StoppingInd,
             MO102_AutoInd,
             MO102_ManualInd,
             MO102_SCADA,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0);]]></Text>
    </Rung>
    <Rung Number="8" Type="N">
      <Comment><![CDATA[Feed Conveyor MO101 Control
Start last in sequence, stop first]]></Comment>
      <Text><![CDATA[aoi_Motor(MO101,
             MO101_Start,
             MO101_Stop,
             MO101_Auto,
             MO101_Manual,
             3000,
             8000,
             3000,
             15000,
             10000,
             MO101_RunFeedback,
             MO101_StartInterlocks,
             MO101_RunInterlocks,
             MO101_Run,
             MO101_Siren,
             MO101_RunningInd,
             MO101_StartingInd,
             MO101_StoppingInd,
             MO101_AutoInd,
             MO101_ManualInd,
             MO101_SCADA,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0.0,
             0);]]></Text>
    </Rung>
    <Rung Number="9" Type="N">
      <Comment><![CDATA[MO103 Discharge Conveyor Start Interlocks
- Tank level above low-low (material available)
- Drift switch OK
- No system fault]]></Comment>
      <Text><![CDATA[XIC(LS102_Low)XIC(DS103_OK)XIO(COAL_SYSTEM_FAULT)OTE(MO103_StartInterlocks);]]></Text>
    </Rung>
    <Rung Number="10" Type="N">
      <Comment><![CDATA[MO103 Discharge Conveyor Run Interlocks
- Same as start interlocks]]></Comment>
      <Text><![CDATA[XIC(MO103_StartInterlocks)OTE(MO103_RunInterlocks);]]></Text>
    </Rung>
    <Rung Number="11" Type="N">
      <Comment><![CDATA[MO102 Transfer Conveyor Start Interlocks
- Discharge conveyor MO103 running
- Drift switch OK
- No system fault]]></Comment>
      <Text><![CDATA[XIC(MO103_RunningInd)XIC(DS102_OK)XIO(COAL_SYSTEM_FAULT)OTE(MO102_StartInterlocks);]]></Text>
    </Rung>
    <Rung Number="12" Type="N">
      <Comment><![CDATA[MO102 Transfer Conveyor Run Interlocks
- Same as start interlocks]]></Comment>
      <Text><![CDATA[XIC(MO102_StartInterlocks)OTE(MO102_RunInterlocks);]]></Text>
    </Rung>
    <Rung Number="13" Type="N">
      <Comment><![CDATA[MO101 Feed Conveyor Start Interlocks
- Transfer conveyor MO102 running
- Tank level not high (space available)
- Drift switch OK
- No system fault]]></Comment>
      <Text><![CDATA[XIC(MO102_RunningInd)XIO(LS101_High)XIC(DS101_OK)XIO(COAL_SYSTEM_FAULT)OTE(MO101_StartInterlocks);]]></Text>
    </Rung>
    <Rung Number="14" Type="N">
      <Comment><![CDATA[MO101 Feed Conveyor Run Interlocks
- Same as start interlocks]]></Comment>
      <Text><![CDATA[XIC(MO101_StartInterlocks)OTE(MO101_RunInterlocks);]]></Text>
    </Rung>
    <Rung Number="15" Type="N">
      <Comment><![CDATA[Coal System Auto Start Sequence
In auto mode, start system when commanded and interlocks satisfied]]></Comment>
      <Text><![CDATA[XIC(COAL_SYSTEM_AUTO)XIC(COAL_SYSTEM_START)XIO(COAL_SYSTEM_FAULT)XIO(LS101_High)XIC(LS102_Low)[OTE(MO103_Start) ,TON(StartDelayTimer1,2000,?) ];]]></Text>
    </Rung>
    <Rung Number="16" Type="N">
      <Text><![CDATA[XIC(StartDelayTimer1.DN)XIC(MO103_RunningInd)[OTE(MO102_Start) ,TON(StartDelayTimer2,2000,?) ];]]></Text>
    </Rung>
    <Rung Number="17" Type="N">
      <Text><![CDATA[XIC(StartDelayTimer2.DN)XIC(MO102_RunningInd)XIO(LS101_High)OTE(MO101_Start);]]></Text>
    </Rung>
    <Rung Number="18" Type="N">
      <Comment><![CDATA[Coal System Auto Stop Sequence
In auto mode, stop system in reverse order]]></Comment>
      <Text><![CDATA[XIC(COAL_SYSTEM_AUTO)[XIC(COAL_SYSTEM_STOP) ,XIC(COAL_SYSTEM_FAULT) ,XIC(LS101_High) ][OTE(MO101_Stop) ,TON(StopDelayTimer1,2000,?) ];]]></Text>
    </Rung>
    <Rung Number="19" Type="N">
      <Text><![CDATA[XIC(StopDelayTimer1.DN)XIO(MO101_RunningInd)[OTE(MO102_Stop) ,TON(StopDelayTimer2,2000,?) ];]]></Text>
    </Rung>
    <Rung Number="20" Type="N">
      <Text><![CDATA[XIC(StopDelayTimer2.DN)XIO(MO102_RunningInd)[OTE(MO103_Stop) ,XIO(LS103_LowLow) ];]]></Text>
    </Rung>
    <Rung Number="21" Type="N">
      <Comment><![CDATA[Copy motor running status to controller tags for interlocking]]></Comment>
      <Text><![CDATA[XIC(MO101_RunningInd)OTE(MO101_Running);]]></Text>
    </Rung>
    <Rung Number="22" Type="N">
      <Text><![CDATA[XIC(MO102_RunningInd)OTE(MO102_Running);]]></Text>
    </Rung>
    <Rung Number="23" Type="N">
      <Text><![CDATA[XIC(MO103_RunningInd)OTE(MO103_Running);]]></Text>
    </Rung>
    <Rung Number="24" Type="N">
      <Comment><![CDATA[I/O Mapping - Input Devices
Map physical inputs to internal tags]]></Comment>
      <Text><![CDATA[[XIC(Flex_Mod_3:I.Data.0) ,XIC(Flex_Mod_3:I.Data.1) ,XIC(Flex_Mod_3:I.Data.2) ][OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ];]]></Text>
    </Rung>
    <Rung Number="25" Type="N">
      <Comment><![CDATA[I/O Mapping - Level Sensors]]></Comment>
      <Text><![CDATA[[XIC(Flex_Mod_3:I.Data.3) ,XIC(Flex_Mod_3:I.Data.4) ,XIC(Flex_Mod_3:I.Data.5) ][OTE(LS101_High) ,OTE(LS102_Low) ,OTE(LS103_LowLow) ];]]></Text>
    </Rung>
    <Rung Number="26" Type="N">
      <Comment><![CDATA[I/O Mapping - Motor Run Feedback]]></Comment>
      <Text><![CDATA[[XIC(Flex_Mod_3:I.Data.6) ,XIC(Flex_Mod_3:I.Data.7) ,XIC(Flex_Mod_3:I.Data.8) ][OTE(MO101_RunFeedback) ,OTE(MO102_RunFeedback) ,OTE(MO103_RunFeedback) ];]]></Text>
    </Rung>
    <Rung Number="27" Type="N">
      <Comment><![CDATA[I/O Mapping - Output Devices
Map internal tags to physical outputs]]></Comment>
      <Text><![CDATA[[XIC(MO101_Run) ,XIC(MO102_Run) ,XIC(MO103_Run) ][OTE(Flex_Mod_2:O.Data.0) ,OTE(Flex_Mod_2:O.Data.1) ,OTE(Flex_Mod_2:O.Data.2) ];]]></Text>
    </Rung>
    <Rung Number="28" Type="N">
      <Comment><![CDATA[I/O Mapping - Motor Sirens]]></Comment>
      <Text><![CDATA[[XIC(MO101_Siren) ,XIC(MO102_Siren) ,XIC(MO103_Siren) ][OTE(Flex_Mod_2:O.Data.3) ,OTE(Flex_Mod_2:O.Data.4) ,OTE(Flex_Mod_2:O.Data.5) ];]]></Text>
    </Rung>
    <Rung Number="29" Type="N">
      <Comment><![CDATA[I/O Mapping - Status Lights]]></Comment>
      <Text><![CDATA[[XIC(COAL_SYSTEM_RUNNING) ,XIC(COAL_SYSTEM_FAULT) ][OTE(Flex_Mod_2:O.Data.6) ,OTE(Flex_Mod_2:O.Data.7) ];]]></Text>
    </Rung>
  </RLLContent>
</Routine>
