# Coal Material Handling System - I/O Mapping

## Overview

This document defines the I/O mapping for the Coal Material Handling System in the LotOfTags project. The system uses FlexIO modules for distributed I/O control.

## I/O Module Configuration

### Input Module: Flex_Mod_3 (5094-IB16/A)
- **Module Type**: 16-point Digital Input
- **Address**: FlexIO:3
- **Voltage**: 24VDC
- **Connection**: FlexIO Bus Address 3

### Output Module: Flex_Mod_2 (5094-OB16/A)
- **Module Type**: 16-point Digital Output
- **Address**: FlexIO:2
- **Voltage**: 24VDC
- **Connection**: FlexIO Bus Address 2

## Digital Input Mapping

| Point | Tag Name | Description | Device | Location |
|-------|----------|-------------|---------|----------|
| Flex_Mod_3:I.Data.0 | DS101_OK | Feed Conveyor CV101 Drift Switch | DS101 | CV101 Head Pulley |
| Flex_Mod_3:I.Data.1 | DS102_OK | Transfer Conveyor CV102 Drift Switch | DS102 | CV102 Head Pulley |
| Flex_Mod_3:I.Data.2 | DS103_OK | Discharge Conveyor CV103 Drift Switch | DS103 | CV103 Head Pulley |
| Flex_Mod_3:I.Data.3 | LS101_High | Coal Tank TK101 High Level | LS101 | TK101 Top |
| Flex_Mod_3:I.Data.4 | LS102_Low | Coal Tank TK101 Low Level | LS102 | TK101 Side |
| Flex_Mod_3:I.Data.5 | LS103_LowLow | Coal Tank TK101 Low-Low Level | LS103 | TK101 Bottom |
| Flex_Mod_3:I.Data.6 | MO101_RunFeedback | Feed Conveyor Motor Run Feedback | MO101 Contactor | MCC Panel |
| Flex_Mod_3:I.Data.7 | MO102_RunFeedback | Transfer Conveyor Motor Run Feedback | MO102 Contactor | MCC Panel |
| Flex_Mod_3:I.Data.8 | MO103_RunFeedback | Discharge Conveyor Motor Run Feedback | MO103 Contactor | MCC Panel |
| Flex_Mod_3:I.Data.9 | - | Spare | - | - |
| Flex_Mod_3:I.Data.10 | - | Spare | - | - |
| Flex_Mod_3:I.Data.11 | - | Spare | - | - |
| Flex_Mod_3:I.Data.12 | - | Spare | - | - |
| Flex_Mod_3:I.Data.13 | - | Spare | - | - |
| Flex_Mod_3:I.Data.14 | - | Spare | - | - |
| Flex_Mod_3:I.Data.15 | - | Spare | - | - |

## Digital Output Mapping

| Point | Tag Name | Description | Device | Location |
|-------|----------|-------------|---------|----------|
| Flex_Mod_2:O.Data.0 | MO101_Run | Feed Conveyor Motor Start | MO101 Starter | MCC Panel |
| Flex_Mod_2:O.Data.1 | MO102_Run | Transfer Conveyor Motor Start | MO102 Starter | MCC Panel |
| Flex_Mod_2:O.Data.2 | MO103_Run | Discharge Conveyor Motor Start | MO103 Starter | MCC Panel |
| Flex_Mod_2:O.Data.3 | MO101_Siren | Feed Conveyor Pre-Start Siren | Horn 101 | CV101 Area |
| Flex_Mod_2:O.Data.4 | MO102_Siren | Transfer Conveyor Pre-Start Siren | Horn 102 | CV102 Area |
| Flex_Mod_2:O.Data.5 | MO103_Siren | Discharge Conveyor Pre-Start Siren | Horn 103 | CV103 Area |
| Flex_Mod_2:O.Data.6 | COAL_SYSTEM_RUNNING | Coal System Running Light | Green Light | Control Panel |
| Flex_Mod_2:O.Data.7 | COAL_SYSTEM_FAULT | Coal System Fault Light | Red Light | Control Panel |
| Flex_Mod_2:O.Data.8 | - | Spare | - | - |
| Flex_Mod_2:O.Data.9 | - | Spare | - | - |
| Flex_Mod_2:O.Data.10 | - | Spare | - | - |
| Flex_Mod_2:O.Data.11 | - | Spare | - | - |
| Flex_Mod_2:O.Data.12 | - | Spare | - | - |
| Flex_Mod_2:O.Data.13 | - | Spare | - | - |
| Flex_Mod_2:O.Data.14 | - | Spare | - | - |
| Flex_Mod_2:O.Data.15 | - | Spare | - | - |

## Device Specifications

### Drift Switches (DS101, DS102, DS103)
- **Type**: Belt Speed Monitor
- **Model**: Suggested - Rexnord Belt-Trak
- **Function**: Monitors belt movement, opens contact on belt slip
- **Wiring**: Normally Closed (NC) contact
- **Mounting**: Proximity to head pulley

### Level Sensors (LS101, LS102, LS103)
- **Type**: Ultrasonic Level Transmitter with Relay Output
- **Model**: Suggested - Siemens SITRANS LU240
- **Function**: Provides discrete level indication
- **Wiring**: Relay contact output (NO for level detected)
- **Mounting**: Tank wall or top-mounted

### Motor Contactors (MO101, MO102, MO103)
- **Type**: 3-Phase Motor Starter
- **Control Voltage**: 24VDC
- **Auxiliary Contacts**: 1 NO for run feedback
- **Overload Protection**: Integrated thermal overload
- **Location**: Motor Control Center (MCC)

### Warning Devices
- **Sirens**: 24VDC Industrial Horn
- **Lights**: 24VDC LED Stack Light
- **Function**: Pre-start warning and status indication

## Wiring Requirements

### Power Supply
- **24VDC Power Supply**: Minimum 5A capacity
- **Distribution**: Fused distribution blocks
- **Grounding**: Proper earth grounding for all devices

### Cable Specifications
- **Input Cables**: 18 AWG, 2-conductor, shielded
- **Output Cables**: 16 AWG, 2-conductor for motor starters
- **Signal Cables**: 18 AWG, 2-conductor for lights/sirens
- **Conduit**: NEMA 4X rated for outdoor installation

### Installation Notes
- All field devices rated for coal dust environment
- Explosion-proof enclosures where required
- Proper cable routing to avoid mechanical damage
- Adequate strain relief at all connections

## Safety Considerations

### Lockout/Tagout (LOTO)
- Each motor starter equipped with LOTO capability
- Main disconnect for entire coal system
- Individual isolation for maintenance

### Emergency Stops
- Emergency stop buttons at strategic locations
- Hard-wired safety circuit independent of PLC
- Immediate stop of all conveyors

### Hazardous Area Classification
- Coal dust creates combustible dust hazard
- Equipment rated for Class II, Division 2 minimum
- Proper grounding and bonding required

## Commissioning Checklist

### Pre-Energization
- [ ] Verify all wiring per drawings
- [ ] Check power supply voltage and polarity
- [ ] Confirm proper grounding
- [ ] Test continuity of all circuits

### Functional Testing
- [ ] Test each input device individually
- [ ] Verify output operation to each device
- [ ] Check motor rotation direction
- [ ] Test emergency stop circuits
- [ ] Validate interlock operation

### System Integration
- [ ] Test sequential startup/shutdown
- [ ] Verify drift switch operation
- [ ] Test level sensor operation
- [ ] Confirm SCADA communication
- [ ] Document all test results

## Maintenance Schedule

### Daily
- Visual inspection of all devices
- Check for loose connections
- Verify proper operation of status lights

### Weekly
- Test drift switch operation
- Clean level sensor faces
- Check motor starter operation

### Monthly
- Calibrate level sensors
- Test emergency stop circuits
- Inspect cable conditions
- Update maintenance logs

## Troubleshooting Guide

### Common Issues

#### Input Not Reading
1. Check 24VDC power supply
2. Verify field device operation
3. Check wiring continuity
4. Confirm PLC input module status

#### Output Not Operating
1. Check 24VDC power supply
2. Verify PLC output status
3. Check field device operation
4. Inspect wiring connections

#### Intermittent Operation
1. Check for loose connections
2. Verify proper grounding
3. Check for electromagnetic interference
4. Inspect cable conditions

## Documentation References

- Electrical Drawings: Coal System E-001 through E-005
- P&ID Drawings: Coal System P-001
- Installation Manual: FlexIO System Configuration
- Device Manuals: Individual device documentation
