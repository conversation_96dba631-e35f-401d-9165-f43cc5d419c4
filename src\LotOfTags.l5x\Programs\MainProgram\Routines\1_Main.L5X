<?xml version='1.0' encoding='UTF-8'?>
<Routine Name="Main" Type="RLL">
            <RLLContent>
              <Rung Number="0" Type="N">
                <Text>XIC(TEST);</Text>
              </Rung>
              <Rung Number="1" Type="N">
                <Text><![CDATA[MOV(16#20,SimpleSint);]]></Text>
              </Rung>
              <Rung Number="2" Type="N">
                <Text><![CDATA[aoi_Test(aoiTestInstance,TestSimpleTag,SimpleInt,RealArray,0);]]></Text>
              </Rung>
              <Rung Number="3" Type="N">
                <Text><![CDATA[[XIC(SimpleBool) ,XIC(SimpleBool) ][OTE(SimpleBool) ,OTU(SimpleBool) ];]]></Text>
              </Rung>
              <Rung Number="4" Type="N">
                <Text><![CDATA[OTE(TestComplexTag.SimpleMember.BoolMember);]]></Text>
              </Rung>
              <Rung Number="5" Type="N">
                <Text><![CDATA[MOV(SimpleSint,AsciiTag);]]></Text>
              </Rung>
              <Rung Number="6" Type="N">
                <Text><![CDATA[XIC(FlexIO:3:I.Pt01.Data)OTE(BufferTag);]]></Text>
              </Rung>
              <Rung Number="7" Type="N">
                <Text><![CDATA[JSR(FBD,1,InputParameter,OutputParameter);]]></Text>
              </Rung>
              <Rung Number="8" Type="N">
                <Comment><![CDATA[Call FE101 Feeder Speed Control Routine]]></Comment>
                <Text><![CDATA[JSR(FE101_FeederSpeedControl);]]></Text>
              </Rung>
              <Rung Number="9" Type="N">
                <Comment><![CDATA[Call PU101 High Pressure Pump Routine]]></Comment>
                <Text><![CDATA[JSR(PU101_HighPressurePump);]]></Text>
              </Rung>
              <Rung Number="10" Type="N">
                <Comment><![CDATA[Call PU102 Lower Pressure Pump Routine]]></Comment>
                <Text><![CDATA[JSR(PU102_LowerPressurePump);]]></Text>
              </Rung>
              <Rung Number="11" Type="N">
                <Comment><![CDATA[Call PU103 Cyclone Feed Pump Routine]]></Comment>
                <Text><![CDATA[JSR(PU103_CycloneFeedPump);]]></Text>
              </Rung>
              <Rung Number="12" Type="N">
                <Comment><![CDATA[Call Coal Material Handling System Control]]></Comment>
                <Text><![CDATA[JSR(CoalHandlingSystem);]]></Text>
              </Rung>
              <Rung Number="13" Type="N">
                <Text><![CDATA[GRT(SimpleInt,100)OTE(SimpleArray[4].0);]]></Text>
              </Rung>
              <Rung Number="14" Type="N">
                <Text><![CDATA[GRT(SimpleInt,400)XIO(MultiDimensionalArray[1,3].3)CMP(ATN(_Test) > 1.0)[TON(TimerArray[0],?,?) ,OTU(TestComplexTag.SimpleMember.BoolMember) ];]]></Text>
              </Rung>
            </RLLContent>
          </Routine>
