<?xml version='1.0' encoding='UTF-8'?>
<Program Name="MainProgram" TestEdits="false" MainRoutineName="Main" Disabled="false" UseAsFolder="false">
        <Tags>
          <Tag Name="Action_000" TagType="Base" DataType="SFC_ACTION" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="BufferTag" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Channel" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="InputParameter" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="OutputParameter" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Step_000" TagType="Base" DataType="SFC_STEP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Step_001" TagType="Base" DataType="SFC_STEP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Step_002" TagType="Base" DataType="SFC_STEP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Step_003" TagType="Base" DataType="SFC_STEP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Step_004" TagType="Base" DataType="SFC_STEP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Stop_000" TagType="Base" DataType="SFC_STOP" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Test" TagType="Base" DataType="SERIAL_PORT_CONTROL" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Tran_000" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Tran_001" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <Tag Name="Tran_002" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            </Tag>
          <!-- Interlock AOI instances for PU101 -->
          <Tag Name="PU101_StartInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Start Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU101_RunInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Run Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU101_ResetInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Reset Interlocks]]></Description>
          </Tag>
          <Tag Name="PU101_StartFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Start First Out Number]]></Description>
          </Tag>
          <Tag Name="PU101_StartFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Start First Out Description]]></Description>
          </Tag>
          <Tag Name="PU101_StartInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Start Interlock Status]]></Description>
          </Tag>
          <Tag Name="PU101_RunFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Run First Out Number]]></Description>
          </Tag>
          <Tag Name="PU101_RunFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Run First Out Description]]></Description>
          </Tag>
          <Tag Name="PU101_RunInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU101 Run Interlock Status]]></Description>
          </Tag>
          <!-- Interlock AOI instances for PU102 -->
          <Tag Name="PU102_StartInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Start Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU102_RunInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Run Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU102_ResetInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Reset Interlocks]]></Description>
          </Tag>
          <Tag Name="PU102_StartFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Start First Out Number]]></Description>
          </Tag>
          <Tag Name="PU102_StartFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Start First Out Description]]></Description>
          </Tag>
          <Tag Name="PU102_StartInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Start Interlock Status]]></Description>
          </Tag>
          <Tag Name="PU102_RunFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Run First Out Number]]></Description>
          </Tag>
          <Tag Name="PU102_RunFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Run First Out Description]]></Description>
          </Tag>
          <Tag Name="PU102_RunInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU102 Run Interlock Status]]></Description>
          </Tag>
          <!-- Interlock AOI instances for PU103 -->
          <Tag Name="PU103_StartInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Start Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU103_RunInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Run Interlock Logic]]></Description>
          </Tag>
          <Tag Name="PU103_ResetInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Reset Interlocks]]></Description>
          </Tag>
          <Tag Name="PU103_StartFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Start First Out Number]]></Description>
          </Tag>
          <Tag Name="PU103_StartFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Start First Out Description]]></Description>
          </Tag>
          <Tag Name="PU103_StartInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Start Interlock Status]]></Description>
          </Tag>
          <Tag Name="PU103_RunFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Run First Out Number]]></Description>
          </Tag>
          <Tag Name="PU103_RunFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Run First Out Description]]></Description>
          </Tag>
          <Tag Name="PU103_RunInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[PU103 Run Interlock Status]]></Description>
          </Tag>
          <!-- Interlock AOI instances for FE101 -->
          <Tag Name="FE101_StartInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Start Interlock Logic]]></Description>
          </Tag>
          <Tag Name="FE101_RunInterlockLogic" TagType="Base" DataType="aoi_Interlocks" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Run Interlock Logic]]></Description>
          </Tag>
          <Tag Name="FE101_ResetInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Reset Interlocks]]></Description>
          </Tag>
          <Tag Name="FE101_StartFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Start First Out Number]]></Description>
          </Tag>
          <Tag Name="FE101_StartFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Start First Out Description]]></Description>
          </Tag>
          <Tag Name="FE101_StartInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Start Interlock Status]]></Description>
          </Tag>
          <Tag Name="FE101_RunFirstOutNum" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Run First Out Number]]></Description>
          </Tag>
          <Tag Name="FE101_RunFirstOutDesc" TagType="Base" DataType="STRING" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Run First Out Description]]></Description>
          </Tag>
          <Tag Name="FE101_RunInterlockStatus" TagType="Base" DataType="DINT" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[FE101 Run Interlock Status]]></Description>
          </Tag>
          <!-- Additional tags for FE101 interlocks -->
          <Tag Name="LS401_NotHighLevel" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Chute level not high (inverted from LS401_HighLevel)]]></Description>
          </Tag>
          <Tag Name="LS401_NotHighHighLevel" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Chute level not high-high (inverted from LS401_HighHighLevel)]]></Description>
          </Tag>

          <!-- Coal Material Handling System Tags -->

          <!-- Conveyor Motor MO101 (Feed Conveyor) -->
          <Tag Name="MO101" TagType="Base" DataType="aoi_Motor" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor MO101 AOI instance]]></Description>
          </Tag>
          <Tag Name="MO101_Start" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor start command]]></Description>
          </Tag>
          <Tag Name="MO101_Stop" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor stop command]]></Description>
          </Tag>
          <Tag Name="MO101_Auto" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor auto mode]]></Description>
          </Tag>
          <Tag Name="MO101_Manual" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor manual mode]]></Description>
          </Tag>
          <Tag Name="MO101_RunFeedback" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor run feedback from contactor]]></Description>
          </Tag>
          <Tag Name="MO101_StartInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor start interlocks]]></Description>
          </Tag>
          <Tag Name="MO101_RunInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor run interlocks]]></Description>
          </Tag>
          <Tag Name="MO101_Run" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor run output]]></Description>
          </Tag>
          <Tag Name="MO101_Siren" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor pre-start siren]]></Description>
          </Tag>
          <Tag Name="MO101_RunningInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor running indication]]></Description>
          </Tag>
          <Tag Name="MO101_StartingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor starting indication]]></Description>
          </Tag>
          <Tag Name="MO101_StoppingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor stopping indication]]></Description>
          </Tag>
          <Tag Name="MO101_AutoInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor auto mode indication]]></Description>
          </Tag>
          <Tag Name="MO101_ManualInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor manual mode indication]]></Description>
          </Tag>
          <Tag Name="MO101_SCADA" TagType="Base" DataType="UDT_SCADA" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Feed Conveyor Motor SCADA interface]]></Description>
          </Tag>

          <!-- Conveyor Motor MO102 (Transfer Conveyor) -->
          <Tag Name="MO102" TagType="Base" DataType="aoi_Motor" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor MO102 AOI instance]]></Description>
          </Tag>
          <Tag Name="MO102_Start" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor start command]]></Description>
          </Tag>
          <Tag Name="MO102_Stop" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor stop command]]></Description>
          </Tag>
          <Tag Name="MO102_Auto" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor auto mode]]></Description>
          </Tag>
          <Tag Name="MO102_Manual" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor manual mode]]></Description>
          </Tag>
          <Tag Name="MO102_RunFeedback" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor run feedback from contactor]]></Description>
          </Tag>
          <Tag Name="MO102_StartInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor start interlocks]]></Description>
          </Tag>
          <Tag Name="MO102_RunInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor run interlocks]]></Description>
          </Tag>
          <Tag Name="MO102_Run" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor run output]]></Description>
          </Tag>
          <Tag Name="MO102_Siren" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor pre-start siren]]></Description>
          </Tag>
          <Tag Name="MO102_RunningInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor running indication]]></Description>
          </Tag>
          <Tag Name="MO102_StartingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor starting indication]]></Description>
          </Tag>
          <Tag Name="MO102_StoppingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor stopping indication]]></Description>
          </Tag>
          <Tag Name="MO102_AutoInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor auto mode indication]]></Description>
          </Tag>
          <Tag Name="MO102_ManualInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor manual mode indication]]></Description>
          </Tag>
          <Tag Name="MO102_SCADA" TagType="Base" DataType="UDT_SCADA" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Transfer Conveyor Motor SCADA interface]]></Description>
          </Tag>

          <!-- Conveyor Motor MO103 (Discharge Conveyor) -->
          <Tag Name="MO103" TagType="Base" DataType="aoi_Motor" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor MO103 AOI instance]]></Description>
          </Tag>
          <Tag Name="MO103_Start" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor start command]]></Description>
          </Tag>
          <Tag Name="MO103_Stop" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor stop command]]></Description>
          </Tag>
          <Tag Name="MO103_Auto" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor auto mode]]></Description>
          </Tag>
          <Tag Name="MO103_Manual" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor manual mode]]></Description>
          </Tag>
          <Tag Name="MO103_RunFeedback" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor run feedback from contactor]]></Description>
          </Tag>
          <Tag Name="MO103_StartInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor start interlocks]]></Description>
          </Tag>
          <Tag Name="MO103_RunInterlocks" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor run interlocks]]></Description>
          </Tag>
          <Tag Name="MO103_Run" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor run output]]></Description>
          </Tag>
          <Tag Name="MO103_Siren" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor pre-start siren]]></Description>
          </Tag>
          <Tag Name="MO103_RunningInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor running indication]]></Description>
          </Tag>
          <Tag Name="MO103_StartingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor starting indication]]></Description>
          </Tag>
          <Tag Name="MO103_StoppingInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor stopping indication]]></Description>
          </Tag>
          <Tag Name="MO103_AutoInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor auto mode indication]]></Description>
          </Tag>
          <Tag Name="MO103_ManualInd" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor manual mode indication]]></Description>
          </Tag>
          <Tag Name="MO103_SCADA" TagType="Base" DataType="UDT_SCADA" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Discharge Conveyor Motor SCADA interface]]></Description>
          </Tag>

          <!-- Coal System Control Tags -->
          <Tag Name="COAL_SYSTEM_START" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Coal system start command]]></Description>
          </Tag>
          <Tag Name="COAL_SYSTEM_STOP" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Coal system stop command]]></Description>
          </Tag>
          <Tag Name="COAL_SYSTEM_AUTO" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Coal system auto mode]]></Description>
          </Tag>
          <Tag Name="COAL_SYSTEM_MANUAL" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Coal system manual mode]]></Description>
          </Tag>
          <Tag Name="COAL_SYSTEM_RESET" TagType="Base" DataType="BOOL" Radix="Decimal" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Coal system fault reset]]></Description>
          </Tag>

          <!-- Coal System Timers -->
          <Tag Name="StartDelayTimer1" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Delay timer between MO103 and MO102 start]]></Description>
          </Tag>
          <Tag Name="StartDelayTimer2" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Delay timer between MO102 and MO101 start]]></Description>
          </Tag>
          <Tag Name="StopDelayTimer1" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Delay timer between MO101 and MO102 stop]]></Description>
          </Tag>
          <Tag Name="StopDelayTimer2" TagType="Base" DataType="TIMER" Constant="false" ExternalAccess="Read/Write">
            <Description><![CDATA[Delay timer between MO102 and MO103 stop]]></Description>
          </Tag>

        </Tags>
        </Program>
