<?xml version='1.0' encoding='UTF-8'?>
<Routine Name="Test_CoalHandlingSystem" Type="RLL">
  <Description><![CDATA[
Unit Tests for Coal Material Handling System

Test Cases:
1. Sequential Startup Test - Verify proper startup sequence
2. Sequential Shutdown Test - Verify proper shutdown sequence  
3. High Level Interlock Test - Verify feed conveyor stops on high level
4. Low-Low Level Interlock Test - Verify discharge conveyor stops on low-low level
5. Drift Switch Fault Test - Verify conveyor stops on drift switch fault

Each test case validates the control logic and safety interlocks
of the coal material handling system.
]]></Description>
  <RLLContent>
    <Rung Number="0" Type="N">
      <Comment><![CDATA[Test Case 1: Sequential Startup Test
Verify that conveyors start in proper sequence: MO103 -> MO102 -> MO101]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,1)XIC(CoalSystemTestStart)[MOV(1,CoalSystemTestCase) ,OTL(Test_CoalSystem_1_Pass) ,MOV('Sequential Startup Test - PASS',Test_CoalSystem_1_Description) ,TON(CoalSystemTestTimer,5000,?) ];]]></Text>
    </Rung>
    <Rung Number="1" Type="N">
      <Comment><![CDATA[Test Case 1 Setup: Set conditions for startup test]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,1)XIC(CoalSystemTestStart)[OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ,OTE(LS102_Low) ,OTU(LS101_High) ,OTU(LS103_LowLow) ,OTU(COAL_SYSTEM_FAULT) ,OTE(COAL_SYSTEM_AUTO) ,OTE(COAL_SYSTEM_START) ];]]></Text>
    </Rung>
    <Rung Number="2" Type="N">
      <Comment><![CDATA[Test Case 1 Validation: Check if startup sequence is correct]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,1)XIC(CoalSystemTestTimer.DN)[XIC(MO103_RunningInd)XIC(MO102_RunningInd)XIC(MO101_RunningInd) ,OTL(Test_CoalSystem_1_Pass) ,MOV('Sequential Startup Test - PASS',Test_CoalSystem_1_Description) ];]]></Text>
    </Rung>
    <Rung Number="3" Type="N">
      <Comment><![CDATA[Test Case 1 Failure: Mark test as failed if sequence incorrect]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,1)XIC(CoalSystemTestTimer.DN)[XIO(MO103_RunningInd) ,XIO(MO102_RunningInd) ,XIO(MO101_RunningInd) ][OTU(Test_CoalSystem_1_Pass) ,MOV('Sequential Startup Test - FAIL',Test_CoalSystem_1_Description) ];]]></Text>
    </Rung>
    <Rung Number="4" Type="N">
      <Comment><![CDATA[Test Case 2: Sequential Shutdown Test
Verify that conveyors stop in proper sequence: MO101 -> MO102 -> MO103]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,2)XIC(CoalSystemTestStart)[MOV(2,CoalSystemTestCase) ,OTL(Test_CoalSystem_2_Pass) ,MOV('Sequential Shutdown Test - PASS',Test_CoalSystem_2_Description) ,TON(CoalSystemTestTimer,5000,?) ];]]></Text>
    </Rung>
    <Rung Number="5" Type="N">
      <Comment><![CDATA[Test Case 2 Setup: Set conditions for shutdown test]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,2)XIC(CoalSystemTestStart)[OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ,OTE(LS102_Low) ,OTU(LS101_High) ,OTU(LS103_LowLow) ,OTU(COAL_SYSTEM_FAULT) ,OTE(COAL_SYSTEM_AUTO) ,OTE(COAL_SYSTEM_STOP) ];]]></Text>
    </Rung>
    <Rung Number="6" Type="N">
      <Comment><![CDATA[Test Case 2 Validation: Check if shutdown sequence is correct]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,2)XIC(CoalSystemTestTimer.DN)[XIO(MO103_RunningInd)XIO(MO102_RunningInd)XIO(MO101_RunningInd) ,OTL(Test_CoalSystem_2_Pass) ,MOV('Sequential Shutdown Test - PASS',Test_CoalSystem_2_Description) ];]]></Text>
    </Rung>
    <Rung Number="7" Type="N">
      <Comment><![CDATA[Test Case 2 Failure: Mark test as failed if sequence incorrect]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,2)XIC(CoalSystemTestTimer.DN)[XIC(MO103_RunningInd) ,XIC(MO102_RunningInd) ,XIC(MO101_RunningInd) ][OTU(Test_CoalSystem_2_Pass) ,MOV('Sequential Shutdown Test - FAIL',Test_CoalSystem_2_Description) ];]]></Text>
    </Rung>
    <Rung Number="8" Type="N">
      <Comment><![CDATA[Test Case 3: High Level Interlock Test
Verify that feed conveyor MO101 stops when tank reaches high level]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,3)XIC(CoalSystemTestStart)[MOV(3,CoalSystemTestCase) ,OTL(Test_CoalSystem_3_Pass) ,MOV('High Level Interlock Test - PASS',Test_CoalSystem_3_Description) ,TON(CoalSystemTestTimer,3000,?) ];]]></Text>
    </Rung>
    <Rung Number="9" Type="N">
      <Comment><![CDATA[Test Case 3 Setup: Set high level condition]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,3)XIC(CoalSystemTestStart)[OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ,OTE(LS102_Low) ,OTE(LS101_High) ,OTU(LS103_LowLow) ,OTU(COAL_SYSTEM_FAULT) ,OTE(COAL_SYSTEM_AUTO) ];]]></Text>
    </Rung>
    <Rung Number="10" Type="N">
      <Comment><![CDATA[Test Case 3 Validation: Check if MO101 is stopped due to high level]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,3)XIC(CoalSystemTestTimer.DN)[XIO(MO101_StartInterlocks)XIO(MO101_RunningInd) ,OTL(Test_CoalSystem_3_Pass) ,MOV('High Level Interlock Test - PASS',Test_CoalSystem_3_Description) ];]]></Text>
    </Rung>
    <Rung Number="11" Type="N">
      <Comment><![CDATA[Test Case 3 Failure: Mark test as failed if MO101 still running]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,3)XIC(CoalSystemTestTimer.DN)[XIC(MO101_StartInterlocks) ,XIC(MO101_RunningInd) ][OTU(Test_CoalSystem_3_Pass) ,MOV('High Level Interlock Test - FAIL',Test_CoalSystem_3_Description) ];]]></Text>
    </Rung>
    <Rung Number="12" Type="N">
      <Comment><![CDATA[Test Case 4: Low-Low Level Interlock Test
Verify that discharge conveyor MO103 stops when tank reaches low-low level]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,4)XIC(CoalSystemTestStart)[MOV(4,CoalSystemTestCase) ,OTL(Test_CoalSystem_4_Pass) ,MOV('Low-Low Level Interlock Test - PASS',Test_CoalSystem_4_Description) ,TON(CoalSystemTestTimer,3000,?) ];]]></Text>
    </Rung>
    <Rung Number="13" Type="N">
      <Comment><![CDATA[Test Case 4 Setup: Set low-low level condition]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,4)XIC(CoalSystemTestStart)[OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ,OTU(LS102_Low) ,OTU(LS101_High) ,OTE(LS103_LowLow) ,OTU(COAL_SYSTEM_FAULT) ,OTE(COAL_SYSTEM_AUTO) ];]]></Text>
    </Rung>
    <Rung Number="14" Type="N">
      <Comment><![CDATA[Test Case 4 Validation: Check if MO103 is stopped due to low-low level]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,4)XIC(CoalSystemTestTimer.DN)[XIO(MO103_StartInterlocks)XIO(MO103_RunningInd) ,OTL(Test_CoalSystem_4_Pass) ,MOV('Low-Low Level Interlock Test - PASS',Test_CoalSystem_4_Description) ];]]></Text>
    </Rung>
    <Rung Number="15" Type="N">
      <Comment><![CDATA[Test Case 4 Failure: Mark test as failed if MO103 still running]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,4)XIC(CoalSystemTestTimer.DN)[XIC(MO103_StartInterlocks) ,XIC(MO103_RunningInd) ][OTU(Test_CoalSystem_4_Pass) ,MOV('Low-Low Level Interlock Test - FAIL',Test_CoalSystem_4_Description) ];]]></Text>
    </Rung>
    <Rung Number="16" Type="N">
      <Comment><![CDATA[Test Case 5: Drift Switch Fault Test
Verify that conveyors stop when drift switch fails]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,5)XIC(CoalSystemTestStart)[MOV(5,CoalSystemTestCase) ,OTL(Test_CoalSystem_5_Pass) ,MOV('Drift Switch Fault Test - PASS',Test_CoalSystem_5_Description) ,TON(CoalSystemTestTimer,3000,?) ];]]></Text>
    </Rung>
    <Rung Number="17" Type="N">
      <Comment><![CDATA[Test Case 5 Setup: Set drift switch fault condition]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,5)XIC(CoalSystemTestStart)[OTU(DS101_OK) ,OTU(DS102_OK) ,OTU(DS103_OK) ,OTE(LS102_Low) ,OTU(LS101_High) ,OTU(LS103_LowLow) ,OTE(COAL_SYSTEM_FAULT) ,OTE(COAL_SYSTEM_AUTO) ];]]></Text>
    </Rung>
    <Rung Number="18" Type="N">
      <Comment><![CDATA[Test Case 5 Validation: Check if system fault is detected]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,5)XIC(CoalSystemTestTimer.DN)[XIC(COAL_SYSTEM_FAULT)XIO(MO101_RunningInd)XIO(MO102_RunningInd)XIO(MO103_RunningInd) ,OTL(Test_CoalSystem_5_Pass) ,MOV('Drift Switch Fault Test - PASS',Test_CoalSystem_5_Description) ];]]></Text>
    </Rung>
    <Rung Number="19" Type="N">
      <Comment><![CDATA[Test Case 5 Failure: Mark test as failed if fault not detected]]></Comment>
      <Text><![CDATA[EQU(CoalSystemTestCase,5)XIC(CoalSystemTestTimer.DN)[XIO(COAL_SYSTEM_FAULT) ,[XIC(MO101_RunningInd) ,XIC(MO102_RunningInd) ,XIC(MO103_RunningInd) ] ][OTU(Test_CoalSystem_5_Pass) ,MOV('Drift Switch Fault Test - FAIL',Test_CoalSystem_5_Description) ];]]></Text>
    </Rung>
    <Rung Number="20" Type="N">
      <Comment><![CDATA[Test Complete Logic
Mark test as complete when timer expires]]></Comment>
      <Text><![CDATA[XIC(CoalSystemTestTimer.DN)OTE(CoalSystemTestComplete);]]></Text>
    </Rung>
    <Rung Number="21" Type="N">
      <Comment><![CDATA[Test Reset Logic
Reset test conditions when starting new test]]></Comment>
      <Text><![CDATA[XIC(CoalSystemTestStart)[OTU(CoalSystemTestComplete) ,OTU(Test_CoalSystem_1_Pass) ,OTU(Test_CoalSystem_2_Pass) ,OTU(Test_CoalSystem_3_Pass) ,OTU(Test_CoalSystem_4_Pass) ,OTU(Test_CoalSystem_5_Pass) ];]]></Text>
    </Rung>
    <Rung Number="22" Type="N">
      <Comment><![CDATA[Test Cleanup
Reset system conditions after test completion]]></Comment>
      <Text><![CDATA[XIC(CoalSystemTestComplete)[OTU(COAL_SYSTEM_START) ,OTU(COAL_SYSTEM_STOP) ,OTU(COAL_SYSTEM_AUTO) ,OTU(COAL_SYSTEM_MANUAL) ,OTE(DS101_OK) ,OTE(DS102_OK) ,OTE(DS103_OK) ,OTU(LS101_High) ,OTE(LS102_Low) ,OTU(LS103_LowLow) ,OTU(COAL_SYSTEM_FAULT) ];]]></Text>
    </Rung>
  </RLLContent>
</Routine>
